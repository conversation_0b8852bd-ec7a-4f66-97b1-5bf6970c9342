import React, { useState, useCallback, memo, useMemo, useEffect } from "react";
import "./ToolsPanel.css";
import { useAudioStore } from "../stores/audioStore";
import { useHighlightStore } from "../stores/highlightStore";
import { useBookmarkStore } from "../stores/bookmarkStore";
import { InlineLoader } from "./LoadingSpinner";
import { useErrorHandler } from "../hooks/useErrorHandler";
import { AUDIO_STATES } from "../utils/constants";
import ColorPicker from "./ColorPicker";
import RichTextEditor from "./RichTextEditor";

const ToolsPanel = memo(
  ({ activeTextSize, onSetTextSize, currentTopic, onPageChange }) => {
    // State and store hooks
    const [noteText, setNoteText] = useState("");
    const { addError } = useErrorHandler();
    const {
      selectedColor,
      setSelectedColor,
      isLoading: isHighlightLoading,
    } = useHighlightStore();
    const { addBookmark, isPageBookmarked, removeBookmark, bookmarks } =
      useBookmarkStore();
    const {
      isPlaying,
      playlist,
      trackIndex,
      audioState,
      error,
      play,
      pause,
      nextTrack,
      prevTrack,
      hasNextTrack,
      hasPrevTrack,
      setTrackIndex,
    } = useAudioStore();

    // Notes persistence logic
    useEffect(() => {
      if (currentTopic) {
        const topicId = currentTopic.realTopicId || currentTopic.id;
        const pageId =
          currentTopic.originalStructure?.pageId || currentTopic.pageId || 1;
        const storageKey = `notes_${topicId}_${pageId}`;
        setNoteText(localStorage.getItem(storageKey) || "");
      }
    }, [currentTopic]);
    useEffect(() => {
      if (currentTopic && noteText !== undefined) {
        const topicId = currentTopic.realTopicId || currentTopic.id;
        const pageId =
          currentTopic.originalStructure?.pageId || currentTopic.pageId || 1;
        const storageKey = `notes_${topicId}_${pageId}`;
        if (noteText.trim()) localStorage.setItem(storageKey, noteText);
        else localStorage.removeItem(storageKey);
      }
    }, [noteText, currentTopic]);

    // Memoized calculations and state derivations
    const currentTrack = playlist[trackIndex] || {};
    const hasAudio = !!currentTrack?.audioSrc;

    const { currentPage, totalPages } = useMemo(() => {
      if (!currentTopic?.pages || !currentTopic.originalStructure) {
        return { currentPage: 1, totalPages: 1 };
      }
      const currentPageIndex = currentTopic.currentPageIndex || 0;
      return {
        currentPage: currentPageIndex + 1,
        totalPages: currentTopic.originalStructure.totalPages,
      };
    }, [currentTopic]);

    // --- FULL HANDLER FUNCTIONS ---

    const handlePageClick = useCallback(
      (pageNumber) => {
        if (currentTopic && onPageChange) {
          onPageChange(currentTopic.id, pageNumber);
        }
      },
      [currentTopic, onPageChange]
    );

    const handleNextPage = useCallback(() => {
      if (currentPage < totalPages) {
        handlePageClick(currentPage + 1);
      }
    }, [currentPage, totalPages, handlePageClick]);

    const handleTextSizeChange = useCallback(
      (size) => {
        onSetTextSize(size);
      },
      [onSetTextSize]
    );

    const handleColorSelect = useCallback(
      (color) => {
        setSelectedColor(color);
      },
      [setSelectedColor]
    );

    const handleAudioControl = useCallback(
      async (action) => {
        try {
          if (action === "play") await play();
          else if (action === "pause") pause();
          else if (action === "next") nextTrack();
          else if (action === "prev") prevTrack();
        } catch (err) {
          addError("Audio control failed");
        }
      },
      [play, pause, nextTrack, prevTrack, addError]
    );

    const handleTrackSelect = useCallback(
      (index) => {
        setTrackIndex(index);
      },
      [setTrackIndex]
    );

    const createRobustPageId = useCallback((topic) => {
      if (!topic) return null;
      const topicId = topic.realTopicId || topic.id;
      const pageIndex = topic.currentPageIndex || 0;
      return topic.originalStructure
        ? `${topicId}-${topic.originalStructure.subTopicId}-page-${pageIndex}`
        : `${topicId}-page-${pageIndex}`;
    }, []);

    const currentPageBookmarked = useMemo(() => {
      if (!currentTopic) return false;
      const topicId = currentTopic.realTopicId || currentTopic.id;
      return isPageBookmarked(topicId, createRobustPageId(currentTopic));
    }, [currentTopic, isPageBookmarked, createRobustPageId, bookmarks]);

    const handleBookmarkToggle = useCallback(() => {
      if (!currentTopic) return;
      const topicId = currentTopic.realTopicId || currentTopic.id;
      const pageId = createRobustPageId(currentTopic);
      if (currentPageBookmarked) {
        const bookmark = bookmarks.find(
          (b) => b.topicId === topicId && b.pageId === pageId
        );
        if (bookmark) removeBookmark(bookmark.id);
      } else {
        addBookmark({
          topicId,
          pageId,
          title: currentTopic.title,
          pageNumber: currentPage,
        });
      }
    }, [
      currentTopic,
      currentPageBookmarked,
      bookmarks,
      addBookmark,
      removeBookmark,
      createRobustPageId,
      currentPage,
    ]);

    return (
      <aside className="tools-panel-sidebar">
        {/* 1. Page Section */}
        <div className="tool-section">
          <div className="section-header">
            <span className="section-title">Page:</span>
          </div>
          <div className="page-controls-wrapper">
            <div className="pagination-controls">
              <button
                className="pagination-nav-btn"
                onClick={() => handlePageClick(currentPage - 1)}
                disabled={currentPage <= 1}
              >
                {"<"}
              </button>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (pageNum) => (
                  <button
                    key={pageNum}
                    className={`pagination-btn ${
                      pageNum === currentPage ? "active" : ""
                    }`}
                    onClick={() => handlePageClick(pageNum)}
                  >
                    {pageNum}
                  </button>
                )
              )}
              <button
                className="pagination-nav-btn"
                onClick={handleNextPage}
                disabled={currentPage >= totalPages}
              >
                {">"}
              </button>
            </div>
            <button
              className="add-bookmark-btn"
              onClick={handleBookmarkToggle}
              title={currentPageBookmarked ? "Remove bookmark" : "Add bookmark"}
              aria-pressed={currentPageBookmarked}
            >
              {currentPageBookmarked ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  width="24"
                  height="24"
                  aria-hidden="true"
                >
                  <path d="M6 4v16l6-6 6 6V4z" />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  width="24"
                  height="24"
                  aria-hidden="true"
                >
                  <path d="M6 4v16l6-6 6 6V4z" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* 2. Text Size Section */}
        <div className="tool-section">
          <div className="section-header">
            <span className="section-title">Text size:</span>
          </div>
          <div className="text-size-controls">
            <button
              className={`text-size-btn ${
                activeTextSize === "small" ? "active" : ""
              }`}
              onClick={() => handleTextSizeChange("small")}
            >
              Small
            </button>
            <button
              className={`text-size-btn ${
                activeTextSize === "normal" ? "active" : ""
              }`}
              onClick={() => handleTextSizeChange("normal")}
            >
              Normal
            </button>
            <button
              className={`text-size-btn ${
                activeTextSize === "large" ? "active" : ""
              }`}
              onClick={() => handleTextSizeChange("large")}
            >
              Large
            </button>
          </div>
        </div>

        {/* 3. Now Playing Section */}
        <div className="tool-section">
          <div className="section-header">
            <span className="section-title">Now playing:</span>
          </div>

          <div className="media-player">
            <div className="media-info">
              <span className="media-title">
                {hasAudio ? currentTrack.title : "Rectilinear Motion"}
              </span>
            </div>

            <div className="media-controls">
              {/* Previous Button */}
              <button
                onClick={() => handleAudioControl("prev")}
                className="media-control-btn"
                disabled={
                  !hasPrevTrack() || audioState === AUDIO_STATES.LOADING
                }
                title="Previous"
              >
                <span className="control-icon" role="img" aria-label="Previous">
                  ⏮
                </span>
              </button>

              {/* Play / Pause Button */}
              <button
                onClick={() => handleAudioControl(isPlaying ? "pause" : "play")}
                className="media-control-btn play-pause"
                disabled={!hasAudio}
                title={isPlaying ? "Pause" : "Play"}
              >
                {audioState === AUDIO_STATES.LOADING ? (
                  <InlineLoader size="small" />
                ) : (
                  <span
                    className="control-icon"
                    role="img"
                    aria-label={isPlaying ? "Pause" : "Play"}
                  >
                    {isPlaying ? "⏸" : "▶"}
                  </span>
                )}
              </button>

              {/* Next Button */}
              <button
                onClick={() => handleAudioControl("next")}
                className="media-control-btn"
                disabled={
                  !hasNextTrack() || audioState === AUDIO_STATES.LOADING
                }
                title="Next"
              >
                <span className="control-icon" role="img" aria-label="Next">
                  ⏭
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* 4. Audio List Section */}

        <div className="section-header">
          <span className="section-title">Audio List:</span>
        </div>
        {playlist.length > 0 && (
          <div className="tool-section">
            <div className="audio-list">
              {playlist.map((track, index) => (
                <div
                  key={index}
                  className={`audio-item ${
                    index === trackIndex ? "active" : ""
                  }`}
                  onClick={() => handleTrackSelect(index)}
                >
                  <span className="audio-item-title">
                    {track.title || `Audio ${index + 1}`}
                  </span>
                  {index === trackIndex && isPlaying && (
                    <div className="audio-playing-indicator">
                      <div className="audio-wave">
                        <div className="wave-bar"></div>
                        <div className="wave-bar"></div>
                        <div className="wave-bar"></div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 5. Annotate Section with Separator */}
        <div className="tool-section annotate-section has-separator">
          <div className="section-header">
            <span className="section-title">Annotate:</span>
          </div>
          <div className="annotate-controls">
            <div className="highlighter-container">
              <img
                src="/highlighter.png"
                alt="Highlighter"
                className="highlighter-icon"
              />
            </div>
            <ColorPicker
              selectedColor={selectedColor}
              onColorSelect={handleColorSelect}
              disabled={isHighlightLoading}
            />
          </div>
        </div>

        {/* 6. Notes Section */}
        <div className="tool-section notes-section">
          <div className="section-header">
            <span className="section-title">Notes:</span>
          </div>
          <RichTextEditor
            value={noteText}
            onChange={(e) => setNoteText(e.target.value)}
            placeholder="Write your notes here...."
          />
        </div>
      </aside>
    );
  }
);

ToolsPanel.displayName = "ToolsPanel";
export default ToolsPanel;
