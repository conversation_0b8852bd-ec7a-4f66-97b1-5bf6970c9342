import { useState } from "react";
import "./CourseStructure.css";
import BookmarkAnnotationPanel from "./BookmarkAnnotationPanel";

const CourseStructure = ({
  topics,
  activeTopicId,
  onSelectTopic,
  courseData,
  onNavigateToPage,
}) => {
  const [expandedTopics, setExpandedTopics] = useState({
    dynamics: true,
    statics: true,
  });
  const [showBookmarksPanel, setShowBookmarksPanel] = useState(false);

  const toggleTopic = (topicId) => {
    setExpandedTopics((prev) => ({
      ...prev,
      [topicId]: !prev[topicId],
    }));
  };

  // Group topics by their main topic (Dynamics/Statics)
  const groupedTopics = {};
  topics.forEach((topic) => {
    const mainTopicId = topic.originalStructure?.topicId;
    if (mainTopicId) {
      if (!groupedTopics[mainTopicId]) {
        groupedTopics[mainTopicId] = [];
      }
      groupedTopics[mainTopicId].push(topic);
    }
  });

  const getTopicTitle = (topicId) => {
    const titleMap = {
      dynamics: "Dynamics",
      statics: "Statics",
    };
    return titleMap[topicId] || topicId;
  };

  return (
    <>
      <aside className="course-structure-sidebar">
        <button
          className="bookmarks-btn"
          onClick={() => setShowBookmarksPanel(true)}
        >
          Bookmarks & Annotations
        </button>

        <div className="course-content">
          <h2>Course Structure</h2>

          {Object.entries(groupedTopics).map(([mainTopicId, topicList]) => (
            <div key={mainTopicId} className="topic-section">
              <h3
                onClick={() => toggleTopic(mainTopicId)}
                className="topic-header"
              >
                {getTopicTitle(mainTopicId)}{" "}
                {expandedTopics[mainTopicId] ? "▼" : "▶"}
              </h3>
              {expandedTopics[mainTopicId] && (
                <ul>
                  {topicList.map((topic) => (
                    <li
                      key={topic.id}
                      className={topic.id === activeTopicId ? "active" : ""}
                      onClick={() => onSelectTopic(topic.id)}
                    >
                      <span className="topic-title">{topic.title}</span>
                      {topic.originalStructure && (
                        <span className="page-indicator">
                          ({(topic.currentPageIndex || 0) + 1} of{" "}
                          {topic.originalStructure.totalPages})
                        </span>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          ))}
        </div>
      </aside>

      {/* Bookmarks & Annotations Panel */}
      {showBookmarksPanel && (
        <BookmarkAnnotationPanel
          onNavigateToPage={onNavigateToPage}
          onClose={() => setShowBookmarksPanel(false)}
        />
      )}
    </>
  );
};

export default CourseStructure;
