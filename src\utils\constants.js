// Application Constants
export const COLORS = {
  BACKGROUND: "#FFFBF3",
  PRIMARY: "#333",
  SECONDARY: "#555",
  BORDER: "#e0e0e0",
  TEXT: "#444",
  HOVER: "#f5f2eb",
  DISABLED: "rgba(255, 255, 255, 0.3)",
};

export const TEXT_SIZES = {
  SMALL: "small",
  NORMAL: "normal",
  LARGE: "large",
};

export const FONT_SIZES = {
  [TEXT_SIZES.SMALL]: {
    base: "14px",
    heading: "16px",
  },
  [TEXT_SIZES.NORMAL]: {
    base: "16px",
    heading: "18px",
  },
  [TEXT_SIZES.LARGE]: {
    base: "18px",
    heading: "20px",
  },
};

export const AUDIO_STATES = {
  IDLE: "idle",
  LOADING: "loading",
  PLAYING: "playing",
  PAUSED: "paused",
  ERROR: "error",
};

// Highlighting Constants
export const HIGHLIGHT_COLORS = {
  YELLOW: {
    id: "yellow",
    name: "Yellow",
    color: "#ffeb3b",
    backgroundColor: "rgba(255, 235, 59, 0.3)",
  },
  GREEN: {
    id: "green",
    name: "Green",
    color: "#8bc34a",
    backgroundColor: "rgba(139, 195, 74, 0.3)",
  },
  BLUE: {
    id: "blue",
    name: "Blue",
    color: "#90caf9",
    backgroundColor: "rgba(144, 202, 249, 0.3)",
  },
  PINK: {
    id: "pink",
    name: "Pink",
    color: "#f48fb1",
    backgroundColor: "rgba(244, 143, 177, 0.3)",
  },
  PURPLE: {
    id: "purple",
    name: "Purple",
    color: "#ce93d8",
    backgroundColor: "rgba(206, 147, 216, 0.3)",
  },
};

export const DEFAULT_HIGHLIGHT_COLOR = HIGHLIGHT_COLORS.YELLOW;

export const ERROR_MESSAGES = {
  AUDIO_LOAD_FAILED: "Failed to load audio file",
  AUDIO_PLAY_FAILED: "Failed to play audio",
  AUDIO_NOT_SUPPORTED: "Audio format not supported",
  NETWORK_ERROR: "Network error occurred",
  GENERIC_ERROR: "An unexpected error occurred",
};

export const STORAGE_KEYS = {
  TEXT_SIZE: "textSize",
  AUDIO_VOLUME: "audioVolume",
  LAST_TOPIC: "lastTopic",
};

export const DEBOUNCE_DELAYS = {
  SEARCH: 300,
  RESIZE: 150,
  SCROLL: 100,
};

export const PAGINATION = {
  DEFAULT_PAGE: 1,
  ITEMS_PER_PAGE: 10,
};

export const ACCESSIBILITY = {
  FOCUS_VISIBLE_OUTLINE: "2px solid #4A90E2",
  MIN_TOUCH_TARGET: "44px",
  HIGH_CONTRAST_RATIO: 4.5,
};

export const BREAKPOINTS = {
  MOBILE: "768px",
  TABLET: "1024px",
  DESKTOP: "1200px",
};

export const ANIMATION_DURATIONS = {
  FAST: "0.15s",
  NORMAL: "0.3s",
  SLOW: "0.5s",
};
