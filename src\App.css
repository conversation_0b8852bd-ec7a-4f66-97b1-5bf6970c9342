body {
  margin: 0;
  font-family: "Montser<PERSON>", -apple-system, BlinkMacSystemFont, "Segoe UI",
    <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  background-color: #fffbf3;
  color: #333;
}

.app-container {
  display: flex;
  min-height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  /* Fallback for browsers that don't support dvh */
  height: 100vh;
  /* Allow container to adjust when DevTools opens */
  height: 100dvh; /* Dynamic viewport height for modern browsers */
}

/* Web-Highlighter Styles */
.web-highlight-wrap {
  cursor: pointer !important;
  transition: all 0.2s ease;
}

.web-highlight-wrap:hover {
  opacity: 0.8;
}

/* Color-specific highlight styles */
.highlight-yellow {
  background-color: rgba(255, 235, 59, 0.3) !important;
  border-bottom: 2px solid #ffeb3b !important;
}

.highlight-green {
  background-color: rgba(139, 195, 74, 0.3) !important;
  border-bottom: 2px solid #8bc34a !important;
}

.highlight-blue {
  background-color: rgba(144, 202, 249, 0.3) !important;
  border-bottom: 2px solid #90caf9 !important;
}

.highlight-pink {
  background-color: rgba(244, 143, 177, 0.3) !important;
  border-bottom: 2px solid #f48fb1 !important;
}

.highlight-purple {
  background-color: rgba(206, 147, 216, 0.3) !important;
  border-bottom: 2px solid #ce93d8 !important;
}

/* Hover effect for highlights */
.highlight-hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  transform: translateY(-1px) !important;
}
