import { create } from "zustand";

// Manual localStorage helpers
const STORAGE_KEY = "bookmark-annotation-storage";

const loadFromStorage = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      return {
        bookmarks: parsed.bookmarks || [],
        annotations: parsed.annotations || [],
      };
    }
    return { bookmarks: [], annotations: [] };
  } catch (error) {
    console.warn("Failed to load bookmarks/annotations from storage:", error);
    return { bookmarks: [], annotations: [] };
  }
};

const saveToStorage = (state) => {
  try {
    localStorage.setItem(
      STORAGE_KEY,
      JSON.stringify({
        bookmarks: state.bookmarks,
        annotations: state.annotations,
      })
    );
  } catch (error) {
    console.warn("Failed to save bookmarks/annotations to storage:", error);
  }
};

/**
 * Zustand store for managing bookmarks and annotations
 */
export const useBookmarkStore = create((set, get) => {
  const initialState = loadFromStorage();

  return {
    // Array of bookmarks
    // Structure: { id, topicId, pageId, title, createdAt, pageNumber }
    bookmarks: initialState.bookmarks,

    // Array of annotations
    // Structure: { id, topicId, pageId, title, content, createdAt, pageNumber }
    annotations: initialState.annotations,

    /**
     * Add a bookmark
     * @param {Object} bookmark - Bookmark data
     */
    addBookmark: (bookmark) => {
      const newBookmark = {
        id: `bookmark-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date().toISOString(),
        ...bookmark,
      };

      const updatedBookmarks = [...get().bookmarks, newBookmark];
      set({ bookmarks: updatedBookmarks });
      saveToStorage(get());

      return newBookmark;
    },

    /**
     * Remove a bookmark
     * @param {string} bookmarkId - Bookmark ID
     */
    removeBookmark: (bookmarkId) => {
      const updatedBookmarks = get().bookmarks.filter(
        (bookmark) => bookmark.id !== bookmarkId
      );
      set({ bookmarks: updatedBookmarks });
      saveToStorage(get());

      console.log("🗑️ Bookmark removed:", bookmarkId);
    },

    /**
     * Add an annotation
     * @param {Object} annotation - Annotation data
     */
    addAnnotation: (annotation) => {
      const newAnnotation = {
        id: `annotation-${Date.now()}-${Math.random()
          .toString(36)
          .substr(2, 9)}`,
        createdAt: new Date().toISOString(),
        ...annotation,
      };

      const updatedAnnotations = [...get().annotations, newAnnotation];
      set({ annotations: updatedAnnotations });
      saveToStorage(get());

      console.log("✅ Annotation added:", newAnnotation.title);
      return newAnnotation;
    },

    /**
     * Update an annotation
     * @param {string} annotationId - Annotation ID
     * @param {Object} updates - Updates to apply
     */
    updateAnnotation: (annotationId, updates) => {
      const updatedAnnotations = get().annotations.map((annotation) =>
        annotation.id === annotationId
          ? { ...annotation, ...updates, updatedAt: new Date().toISOString() }
          : annotation
      );
      set({ annotations: updatedAnnotations });
      saveToStorage(get());

      console.log("✅ Annotation updated:", annotationId);
    },

    /**
     * Remove an annotation
     * @param {string} annotationId - Annotation ID
     */
    removeAnnotation: (annotationId) => {
      const updatedAnnotations = get().annotations.filter(
        (annotation) => annotation.id !== annotationId
      );
      set({ annotations: updatedAnnotations });
      saveToStorage(get());

      console.log("🗑️ Annotation removed:", annotationId);
    },

    /**
     * Get bookmarks for a specific topic/page
     * @param {string} topicId - Topic ID (optional)
     * @param {string} pageId - Page ID (optional)
     */
    getBookmarks: (topicId = null, pageId = null) => {
      const { bookmarks } = get();
      if (!topicId && !pageId) return bookmarks;

      return bookmarks.filter((bookmark) => {
        if (topicId && pageId) {
          return bookmark.topicId === topicId && bookmark.pageId === pageId;
        }
        if (topicId) {
          return bookmark.topicId === topicId;
        }
        if (pageId) {
          return bookmark.pageId === pageId;
        }
        return true;
      });
    },

    /**
     * Get annotations for a specific topic/page
     * @param {string} topicId - Topic ID (optional)
     * @param {string} pageId - Page ID (optional)
     */
    getAnnotations: (topicId = null, pageId = null) => {
      const { annotations } = get();
      if (!topicId && !pageId) return annotations;

      return annotations.filter((annotation) => {
        if (topicId && pageId) {
          return annotation.topicId === topicId && annotation.pageId === pageId;
        }
        if (topicId) {
          return annotation.topicId === topicId;
        }
        if (pageId) {
          return annotation.pageId === pageId;
        }
        return true;
      });
    },

    /**
     * Check if current page is bookmarked
     * @param {string} topicId - Topic ID
     * @param {string} pageId - Page ID
     */
    isPageBookmarked: (topicId, pageId) => {
      return get().bookmarks.some(
        (bookmark) => bookmark.topicId === topicId && bookmark.pageId === pageId
      );
    },

    /**
     * Clear all bookmarks and annotations
     */
    clearAll: () => {
      set({ bookmarks: [], annotations: [] });
      saveToStorage(get());
      console.log("🧹 All bookmarks and annotations cleared");
    },
  };
});
