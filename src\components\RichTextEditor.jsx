import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import {
  create<PERSON><PERSON><PERSON>,
  Editor,
  Transforms,
  Element as SlateElement,
  Range,
} from "slate";
import { Slate, Editable, withReact, useSlate, ReactEditor } from "slate-react";
import { withHistory } from "slate-history";
import "./RichTextEditor.css";

// Define custom types for our editor
const LIST_TYPES = ["bulleted-list"];
const TEXT_ALIGN_TYPES = ["left", "center", "right", "justify"];

// Helper functions for formatting
const isBlockActive = (editor, format, blockType = "type") => {
  const { selection } = editor;
  if (!selection) return false;

  const [match] = Array.from(
    Editor.nodes(editor, {
      at: Editor.unhangRange(editor, selection),
      match: (n) =>
        !Editor.isEditor(n) &&
        SlateElement.isElement(n) &&
        n[blockType] === format,
    })
  );

  return !!match;
};

// Enhanced mark detection that properly handles cursor state
const isMarkActive = (editor, format) => {
  // First check if there are explicit marks at the current selection
  const marks = Editor.marks(editor);
  if (marks && marks[format] === true) {
    return true;
  }

  // If no explicit marks, check if we're in a collapsed selection
  const { selection } = editor;
  if (!selection || !Range.isCollapsed(selection)) {
    return false;
  }

  // For collapsed selections, check if the mark would be applied to new text
  // This handles the case where formatting persists after deleting formatted text
  try {
    // Get the leaf node at the current selection
    const [leaf] = Editor.leaf(editor, selection);
    return leaf && leaf[format] === true;
  } catch (error) {
    // Fallback to checking marks
    return marks ? marks[format] === true : false;
  }
};

const toggleBlock = (editor, format) => {
  // Always focus the editor first - use try-catch for reliability
  try {
    ReactEditor.focus(editor);
  } catch (error) {
    // Fallback: force focus by selecting the editor
    const editorElement = ReactEditor.toDOMNode(editor, editor);
    if (editorElement) {
      editorElement.focus();
    }
  }

  const isActive = isBlockActive(
    editor,
    format,
    TEXT_ALIGN_TYPES.includes(format) ? "align" : "type"
  );
  const isList = LIST_TYPES.includes(format);

  Transforms.unwrapNodes(editor, {
    match: (n) =>
      !Editor.isEditor(n) &&
      SlateElement.isElement(n) &&
      LIST_TYPES.includes(n.type),
    split: true,
  });

  let newProperties;
  if (TEXT_ALIGN_TYPES.includes(format)) {
    newProperties = {
      align: isActive ? undefined : format,
    };
  } else {
    newProperties = {
      type: isActive ? "paragraph" : isList ? "list-item" : format,
    };
  }

  Transforms.setNodes(editor, newProperties);

  if (!isActive && isList) {
    const block = { type: format, children: [] };
    Transforms.wrapNodes(editor, block);
  }
};

const toggleMark = (editor, format) => {
  // Always focus the editor first - use try-catch for reliability
  try {
    ReactEditor.focus(editor);
  } catch (error) {
    // Fallback: force focus by selecting the editor
    try {
      const editorElement = ReactEditor.toDOMNode(editor, editor);
      if (editorElement) {
        editorElement.focus();
      }
    } catch (domError) {
      // If DOM approach fails, ensure we have a selection
      if (!editor.selection) {
        Transforms.select(editor, Editor.start(editor, []));
      }
    }
  }

  // Ensure we have a selection after focusing
  if (!editor.selection) {
    Transforms.select(editor, Editor.start(editor, []));
  }

  const isActive = isMarkActive(editor, format);

  if (isActive) {
    Editor.removeMark(editor, format);
  } else {
    Editor.addMark(editor, format, true);
  }

  // Force a re-render to update button states
  const { selection } = editor;
  if (selection) {
    Transforms.setSelection(editor, selection);
  }
};

// Toolbar button component
const Button = ({ active, onMouseDown, children, title }) => (
  <button
    className={`rich-text-btn ${active ? "active" : ""}`}
    onMouseDown={onMouseDown}
    title={title}
    type="button"
  >
    {children}
  </button>
);

// Mark button (Bold, Italic, Underline) with enhanced state management
const MarkButton = ({ format, children, title }) => {
  const editor = useSlate();
  const isActive = isMarkActive(editor, format);

  const handleMouseDown = useCallback(
    (event) => {
      event.preventDefault();
      // Add a small delay to ensure DOM is ready
      setTimeout(() => {
        toggleMark(editor, format);
      }, 0);
    },
    [editor, format]
  );

  return (
    <Button active={isActive} onMouseDown={handleMouseDown} title={title}>
      {children}
    </Button>
  );
};

// Block button (Lists)
const BlockButton = ({ format, children, title }) => {
  const editor = useSlate();
  const isActive = isBlockActive(editor, format);

  const handleMouseDown = useCallback(
    (event) => {
      event.preventDefault();
      // Add a small delay to ensure DOM is ready
      setTimeout(() => {
        toggleBlock(editor, format);
      }, 0);
    },
    [editor, format]
  );

  return (
    <Button active={isActive} onMouseDown={handleMouseDown} title={title}>
      {children}
    </Button>
  );
};

// Toolbar component
const Toolbar = ({ children }) => (
  <div className="rich-text-toolbar">{children}</div>
);

// Element renderer
const RichTextElement = ({ attributes, children, element }) => {
  const style = { textAlign: element.align };

  switch (element.type) {
    case "bulleted-list":
      return (
        <ul style={style} {...attributes} className="slate-bulleted-list">
          {children}
        </ul>
      );
    case "list-item":
      return (
        <li style={style} {...attributes} className="slate-list-item">
          {children}
        </li>
      );
    default:
      return (
        <p style={style} {...attributes} className="slate-paragraph">
          {children}
        </p>
      );
  }
};

// Leaf renderer for text formatting
const Leaf = ({ attributes, children, leaf }) => {
  if (leaf.bold) {
    children = <strong>{children}</strong>;
  }

  if (leaf.italic) {
    children = <em>{children}</em>;
  }

  if (leaf.underline) {
    children = <u>{children}</u>;
  }

  return <span {...attributes}>{children}</span>;
};

// Custom withMarks plugin to handle mark persistence
const withMarks = (editor) => {
  const { deleteBackward, deleteForward, insertText } = editor;

  // Override deleteBackward to maintain marks when deleting all formatted text
  editor.deleteBackward = (...args) => {
    const { selection } = editor;

    if (selection && Range.isCollapsed(selection)) {
      // Get current marks before deletion
      const currentMarks = Editor.marks(editor);

      // Perform the deletion
      deleteBackward(...args);

      // If we had marks and the selection is still collapsed, restore them
      if (currentMarks && Object.keys(currentMarks).length > 0) {
        const newSelection = editor.selection;
        if (newSelection && Range.isCollapsed(newSelection)) {
          // Add back the marks so they persist for new text
          Object.entries(currentMarks).forEach(([key, value]) => {
            if (value) {
              Editor.addMark(editor, key, value);
            }
          });
        }
      }
    } else {
      deleteBackward(...args);
    }
  };

  // Override deleteForward with similar logic
  editor.deleteForward = (...args) => {
    const { selection } = editor;

    if (selection && Range.isCollapsed(selection)) {
      const currentMarks = Editor.marks(editor);
      deleteForward(...args);

      if (currentMarks && Object.keys(currentMarks).length > 0) {
        const newSelection = editor.selection;
        if (newSelection && Range.isCollapsed(newSelection)) {
          Object.entries(currentMarks).forEach(([key, value]) => {
            if (value) {
              Editor.addMark(editor, key, value);
            }
          });
        }
      }
    } else {
      deleteForward(...args);
    }
  };

  return editor;
};

const RichTextEditor = ({
  value,
  onChange,
  placeholder = "Write your notes here...",
}) => {
  // Create editor with our custom plugin
  const editor = useMemo(
    () => withMarks(withHistory(withReact(createEditor()))),
    []
  );

  // Initialize editor value
  const [editorValue, setEditorValue] = useState(() => {
    if (value && typeof value === "string" && value.trim()) {
      try {
        // Try to parse as JSON (Slate format)
        return JSON.parse(value);
      } catch {
        // If not JSON, create a simple paragraph with the text
        return [
          {
            type: "paragraph",
            children: [{ text: value }],
          },
        ];
      }
    }
    return [
      {
        type: "paragraph",
        children: [{ text: "" }],
      },
    ];
  });

  // Handle editor changes with improved state management
  const handleChange = useCallback(
    (newValue) => {
      setEditorValue(newValue);

      if (onChange) {
        // Convert to JSON string for storage
        const serialized = JSON.stringify(newValue);
        onChange({ target: { value: serialized } });
      }
    },
    [onChange]
  );

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback(
    (event) => {
      if (!event.ctrlKey && !event.metaKey) {
        return;
      }

      switch (event.key) {
        case "b": {
          event.preventDefault();
          toggleMark(editor, "bold");
          break;
        }
        case "i": {
          event.preventDefault();
          toggleMark(editor, "italic");
          break;
        }
        case "u": {
          event.preventDefault();
          toggleMark(editor, "underline");
          break;
        }
        default:
          break;
      }
    },
    [editor]
  );

  // Render element
  const renderElement = useCallback(
    (props) => <RichTextElement {...props} />,
    []
  );

  // Render leaf
  const renderLeaf = useCallback((props) => <Leaf {...props} />, []);

  return (
    <div className="rich-text-editor-container">
      <Slate editor={editor} initialValue={editorValue} onChange={handleChange}>
        <Toolbar>
          <MarkButton format="bold" title="Bold (Ctrl+B)">
            <strong>B</strong>
          </MarkButton>
          <MarkButton format="italic" title="Italic (Ctrl+I)">
            <em>I</em>
          </MarkButton>
          <MarkButton format="underline" title="Underline (Ctrl+U)">
            <u>U</u>
          </MarkButton>
          <BlockButton format="bulleted-list" title="Bullet Points">
            •
          </BlockButton>
        </Toolbar>
        <Editable
          className="rich-text-editor-content"
          renderElement={renderElement}
          renderLeaf={renderLeaf}
          placeholder={placeholder}
          onKeyDown={handleKeyDown}
          spellCheck
          autoFocus
        />
      </Slate>
    </div>
  );
};

export default RichTextEditor;
