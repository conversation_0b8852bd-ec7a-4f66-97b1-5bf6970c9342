/* Toast Container */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  pointer-events: none;
}

/* Individual Toast */
.toast {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-left: 4px solid #333;
  font-family: "Montserrat", sans-serif;
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  max-width: 100%;
  word-wrap: break-word;
}

.toast--visible {
  transform: translateX(0);
  opacity: 1;
}

.toast--exiting {
  transform: translateX(100%);
  opacity: 0;
}

/* Toast Types */
.toast--success {
  border-left-color: #4caf50;
}

.toast--error {
  border-left-color: #f44336;
}

.toast--warning {
  border-left-color: #ff9800;
}

.toast--info {
  border-left-color: #2196f3;
}

/* Toast Icon */
.toast__icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  border-radius: 50%;
  color: white;
  margin-top: 2px;
}

.toast--success .toast__icon {
  background-color: #4caf50;
}

.toast--error .toast__icon {
  background-color: #f44336;
}

.toast--warning .toast__icon {
  background-color: #ff9800;
}

.toast--info .toast__icon {
  background-color: #2196f3;
}

/* Toast Content */
.toast__content {
  flex: 1;
  min-width: 0;
}

.toast__message {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  word-break: break-word;
}

/* Close Button */
.toast__close {
  flex-shrink: 0;
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  margin-top: 2px;
}

.toast__close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}

.toast__close:focus {
  outline: 2px solid #4A90E2;
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }

  .toast {
    padding: 12px;
    font-size: 13px;
  }

  .toast__message {
    font-size: 13px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .toast {
    border: 2px solid #000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .toast__icon {
    border: 1px solid #fff;
  }

  .toast__close {
    border: 1px solid #666;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .toast {
    transition: opacity 0.2s ease;
    transform: none;
  }

  .toast--visible {
    transform: none;
  }

  .toast--exiting {
    transform: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .toast {
    background: #2d2d2d;
    color: #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .toast__message {
    color: #fff;
  }

  .toast__close {
    color: #ccc;
  }

  .toast__close:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
}
