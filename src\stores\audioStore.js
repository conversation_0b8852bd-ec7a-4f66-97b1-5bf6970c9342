import { create } from "zustand";
import { AUDIO_STATES, ERROR_MESSAGES } from "../utils/constants.js";
import { isValidAudioUrl, clamp } from "../utils/helpers.js";

export const useAudioStore = create((set, get) => ({
  // Core state
  playlist: [], // Current page's audio files only
  trackIndex: 0,
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  volume: 1,
  audioRef: null,
  currentPageId: null, // Track which page we're currently playing

  // Enhanced state
  audioState: AUDIO_STATES.IDLE,
  error: null,
  isLoading: false,
  buffered: 0,
  playbackRate: 1,

  // Error handling
  setError: (error) => {
    console.error("Audio error:", error);
    set({
      error:
        typeof error === "string"
          ? error
          : error?.message || ERROR_MESSAGES.GENERIC_ERROR,
      audioState: AUDIO_STATES.ERROR,
      isPlaying: false,
      isLoading: false,
    });
  },

  clearError: () => {
    set({ error: null, audioState: AUDIO_STATES.IDLE });
  },

  initialize: (ref, initialPlaylist) => {
    const validPlaylist = Array.isArray(initialPlaylist) ? initialPlaylist : [];
    set({
      audioRef: ref,
      playlist: validPlaylist,
      audioState: AUDIO_STATES.IDLE,
      error: null,
      isLoading: false,
    });

    const audio = ref.current;
    if (audio) {
      const setAudioData = () => {
        if (audio.duration && !isNaN(audio.duration)) {
          set({
            duration: audio.duration,
            audioState: AUDIO_STATES.IDLE,
            isLoading: false,
          });
        }
      };

      const setAudioTime = () => {
        if (audio.currentTime && !isNaN(audio.currentTime)) {
          set({ currentTime: audio.currentTime });
        }
      };

      const handleTrackEnd = () => {
        const { nextTrack } = get();
        set({ isPlaying: false, audioState: AUDIO_STATES.IDLE });

        // Auto-advance to next track if available
        const { playlist, trackIndex } = get();
        if (trackIndex < playlist.length - 1) {
          setTimeout(() => nextTrack(), 500);
        }
      };

      const handleLoadStart = () => {
        set({
          currentTime: 0,
          duration: 0,
          isLoading: true,
          audioState: AUDIO_STATES.LOADING,
          error: null,
        });
      };

      const handleCanPlay = () => {
        set({
          isLoading: false,
          audioState: AUDIO_STATES.IDLE,
        });
      };

      const handleError = () => {
        const { setError } = get();
        const errorMessage = audio.error
          ? `Audio error: ${audio.error.message}`
          : ERROR_MESSAGES.AUDIO_LOAD_FAILED;
        setError(errorMessage);
      };

      const handleProgress = () => {
        if (audio.buffered.length > 0) {
          const buffered = (audio.buffered.end(0) / audio.duration) * 100;
          set({ buffered: Math.min(buffered, 100) });
        }
      };

      // Add event listeners
      audio.addEventListener("loadstart", handleLoadStart);
      audio.addEventListener("loadedmetadata", setAudioData);
      audio.addEventListener("timeupdate", setAudioTime);
      audio.addEventListener("ended", handleTrackEnd);
      audio.addEventListener("canplay", handleCanPlay);
      audio.addEventListener("error", handleError);
      audio.addEventListener("progress", handleProgress);

      return () => {
        audio.removeEventListener("loadstart", handleLoadStart);
        audio.removeEventListener("loadedmetadata", setAudioData);
        audio.removeEventListener("timeupdate", setAudioTime);
        audio.removeEventListener("ended", handleTrackEnd);
        audio.removeEventListener("canplay", handleCanPlay);
        audio.removeEventListener("error", handleError);
        audio.removeEventListener("progress", handleProgress);
      };
    }
  },

  play: async () => {
    const { audioRef, setError, clearError } = get();
    const audio = audioRef?.current;

    if (!audio) {
      setError(ERROR_MESSAGES.GENERIC_ERROR);
      return;
    }

    if (!audio.src) {
      setError(ERROR_MESSAGES.AUDIO_LOAD_FAILED);
      return;
    }

    try {
      clearError();
      set({ audioState: AUDIO_STATES.LOADING });

      // Ensure audio is loaded before playing
      if (audio.readyState < 2) {
        // Wait for audio to be ready
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error("Audio loading timeout"));
          }, 10000); // 10 second timeout

          const handleCanPlay = () => {
            clearTimeout(timeout);
            audio.removeEventListener("canplay", handleCanPlay);
            audio.removeEventListener("error", handleError);
            resolve();
          };

          const handleError = () => {
            clearTimeout(timeout);
            audio.removeEventListener("canplay", handleCanPlay);
            audio.removeEventListener("error", handleError);
            reject(new Error(ERROR_MESSAGES.AUDIO_LOAD_FAILED));
          };

          audio.addEventListener("canplay", handleCanPlay, { once: true });
          audio.addEventListener("error", handleError, { once: true });
        });
      }

      // Try to play
      await audio.play();
      set({
        isPlaying: true,
        audioState: AUDIO_STATES.PLAYING,
      });
    } catch (error) {
      console.error("Audio play failed:", error);

      if (error.name === "NotAllowedError") {
        setError(
          "Audio playback was blocked. Please interact with the page first."
        );
      } else if (error.name === "NotSupportedError") {
        setError(ERROR_MESSAGES.AUDIO_NOT_SUPPORTED);
      } else if (error.message?.includes("timeout")) {
        setError("Audio loading timed out. Please try again.");
      } else {
        setError(ERROR_MESSAGES.AUDIO_PLAY_FAILED);
      }

      set({ isPlaying: false });
    }
  },

  pause: () => {
    const audio = get().audioRef?.current;
    if (audio) {
      audio.pause();
      set({
        isPlaying: false,
        audioState: AUDIO_STATES.PAUSED,
      });
    }
  },

  seek: (time) => {
    const { audioRef, clearError } = get();
    const audio = audioRef?.current;

    if (audio && !isNaN(time) && time >= 0 && time <= audio.duration) {
      try {
        clearError();
        audio.currentTime = clamp(time, 0, audio.duration);
        set({ currentTime: audio.currentTime });
      } catch (error) {
        console.error("Seek error:", error);
      }
    }
  },

  setVolume: (volume) => {
    const { audioRef } = get();
    const audio = audioRef?.current;
    const clampedVolume = clamp(volume, 0, 1);

    if (audio) {
      audio.volume = clampedVolume;
    }
    set({ volume: clampedVolume });
  },

  setPlaybackRate: (rate) => {
    const { audioRef } = get();
    const audio = audioRef?.current;
    const clampedRate = clamp(rate, 0.25, 2);

    if (audio) {
      audio.playbackRate = clampedRate;
    }
    set({ playbackRate: clampedRate });
  },

  loadTrack: (index) => {
    const { playlist, audioRef, setError, clearError } = get();

    if (!Array.isArray(playlist) || playlist.length === 0) {
      setError("No playlist available");
      return;
    }

    if (index < 0 || index >= playlist.length) {
      setError(`Invalid track index: ${index}`);
      return;
    }

    const track = playlist[index];
    if (!track || !track.audioSrc) {
      setError("Invalid track or missing audio source");
      return;
    }

    const audioSrc = Array.isArray(track.audioSrc)
      ? track.audioSrc[0]
      : track.audioSrc;

    if (!isValidAudioUrl(audioSrc)) {
      setError("Invalid audio URL format");
      return;
    }

    try {
      clearError();
      set({
        trackIndex: index,
        isPlaying: false,
        currentTime: 0,
        duration: 0,
        buffered: 0,
        audioState: AUDIO_STATES.LOADING,
      });

      const audio = audioRef?.current;
      if (audio) {
        audio.src = audioSrc;
        audio.load();
        console.log("Loading track:", track.title || `Track ${index + 1}`);
      }
    } catch (error) {
      setError(`Failed to load track: ${error.message}`);
    }
  },

  updatePlaylist: (newPlaylist) => {
    const validPlaylist = Array.isArray(newPlaylist) ? newPlaylist : [];
    set({
      playlist: validPlaylist,
      trackIndex: 0,
      error: null,
    });
  },

  // Load playlist for a specific page
  loadPagePlaylist: (pageId, audioSources) => {
    const { setError, clearError } = get();

    if (!pageId) {
      setError("Invalid page ID");
      return;
    }

    if (!audioSources || audioSources.length === 0) {
      set({
        playlist: [],
        trackIndex: 0,
        currentPageId: pageId,
        isPlaying: false,
        currentTime: 0,
        duration: 0,
        audioState: AUDIO_STATES.IDLE,
        error: null,
      });
      return;
    }

    try {
      clearError();

      // Validate audio sources
      const validAudioSources = audioSources.filter((src) =>
        isValidAudioUrl(src)
      );

      if (validAudioSources.length === 0) {
        setError("No valid audio sources found");
        return;
      }

      // Create playlist from page's audio sources
      const pagePlaylist = validAudioSources.map((audioSrc, index) => ({
        id: `${pageId}-track-${index}`,
        title: `Track ${index + 1}`,
        audioSrc: audioSrc,
        pageId: pageId,
      }));

      set({
        playlist: pagePlaylist,
        trackIndex: 0,
        currentPageId: pageId,
        isPlaying: false,
        currentTime: 0,
        duration: 0,
        buffered: 0,
        audioState: AUDIO_STATES.IDLE,
      });

      // Load the first track
      const { audioRef } = get();
      const audio = audioRef?.current;
      if (audio && pagePlaylist.length > 0) {
        audio.src = pagePlaylist[0].audioSrc;
        audio.load();
      }
    } catch (error) {
      setError(`Failed to load page playlist: ${error.message}`);
    }
  },

  nextTrack: () => {
    const { trackIndex, playlist, loadTrack } = get();
    if (playlist.length > 1 && trackIndex < playlist.length - 1) {
      const newIndex = trackIndex + 1;
      loadTrack(newIndex);
    }
  },

  prevTrack: () => {
    const { trackIndex, playlist, loadTrack } = get();
    if (playlist.length > 1 && trackIndex > 0) {
      const newIndex = trackIndex - 1;
      loadTrack(newIndex);
    }
  },

  setTrackIndex: (index) => {
    const { playlist, loadTrack } = get();
    if (index >= 0 && index < playlist.length) {
      loadTrack(index);
    }
    set({ trackIndex: index });
    get().play();
  },

  // Utility methods
  getCurrentTrack: () => {
    const { playlist, trackIndex } = get();
    return playlist[trackIndex] || null;
  },

  hasNextTrack: () => {
    const { trackIndex, playlist } = get();
    return trackIndex < playlist.length - 1;
  },

  hasPrevTrack: () => {
    const { trackIndex } = get();
    return trackIndex > 0;
  },

  reset: () => {
    set({
      playlist: [],
      trackIndex: 0,
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      volume: 1,
      currentPageId: null,
      audioState: AUDIO_STATES.IDLE,
      error: null,
      isLoading: false,
      buffered: 0,
      playbackRate: 1,
    });
  },
}));
