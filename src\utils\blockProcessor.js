/**
 * Simple Block Processing for Resilient Highlighting
 * Adds stable IDs to content blocks for highlight persistence
 */

export const generateBlockId = (
  type = "block",
  index = 0,
  content = "",
  topicId = ""
) => {
  const hashInput = `${topicId}-${type}-${index}-${content.substring(0, 50)}`;
  const hash = createStableHash(hashInput);
  return `${type}-${index}-${hash}`;
};

const createStableHash = (input) => {
  if (!input) return "default";

  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  return Math.abs(hash).toString(36).substring(0, 8);
};

/**
 * Process content and add stable block IDs
 * @param {Array} contentBody - Content body array from topic
 * @returns {string} HTML with stable block IDs
 */
export const processContentWithBlocks = (contentBody, topicId = "") => {
  if (!Array.isArray(contentBody) || contentBody.length === 0) {
    return "";
  }

  let blockIndex = 0;

  const processedContent = contentBody
    .map((element) => {
      let html = "";

      switch (element.type) {
        case "html":
          // Process raw HTML and add block IDs
          html = addBlockIdsToHtml(element.content || "", blockIndex);
          blockIndex += countBlockElements(element.content || "");
          break;

        case "paragraph":
          const paragraphContent = element.text || "";
          const paragraphId = generateBlockId(
            "para",
            blockIndex,
            paragraphContent,
            topicId
          );
          html = `<p id="${paragraphId}" class="highlight-block">${paragraphContent}</p>`;
          blockIndex++;
          break;

        case "ordered-list":
          const listId = generateBlockId("list", blockIndex);
          const listItems = (element.items || [])
            .map((item, itemIndex) => {
              const itemId = generateBlockId(
                "item",
                blockIndex + itemIndex + 1
              );
              return `<li id="${itemId}" class="highlight-block">${
                item.label || ""
              } - ${item.description || ""}</li>`;
            })
            .join("");
          html = `<ol id="${listId}" class="highlight-block">${listItems}</ol>`;
          blockIndex += (element.items || []).length + 1;
          break;

        case "unordered-list":
          const ulistId = generateBlockId("list", blockIndex);
          const ulistItems = (element.items || [])
            .map((item, itemIndex) => {
              const itemId = generateBlockId(
                "item",
                blockIndex + itemIndex + 1
              );
              const text = typeof item === "string" ? item : item.text || "";
              return `<li id="${itemId}" class="highlight-block">${text}</li>`;
            })
            .join("");
          html = `<ul id="${ulistId}" class="highlight-block">${ulistItems}</ul>`;
          blockIndex += (element.items || []).length + 1;
          break;

        default:
          console.warn("Unknown content element type:", element.type);
          break;
      }

      return html;
    })
    .join("");

  return processedContent;
};

/**
 * Add block IDs to raw HTML content
 * @param {string} htmlContent - Raw HTML content
 * @param {number} startIndex - Starting block index
 * @returns {string} HTML with block IDs added
 */
export const addBlockIdsToHtml = (htmlContent, startIndex = 0) => {
  if (!htmlContent) return "";

  try {
    // Create temporary DOM element to parse HTML
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = htmlContent;

    let blockIndex = startIndex;

    // Add IDs to block elements
    const blockElements = tempDiv.querySelectorAll(
      "p, div, h1, h2, h3, h4, h5, h6, li, blockquote, pre"
    );

    blockElements.forEach((element) => {
      if (!element.id) {
        const tagName = element.tagName.toLowerCase();
        const blockId = generateBlockId(tagName, blockIndex);
        element.id = blockId;
        element.classList.add("highlight-block");
        blockIndex++;
      }
    });

    return tempDiv.innerHTML;
  } catch (error) {
    console.error("Failed to add block IDs to HTML:", error);
    return htmlContent; // Return original on error
  }
};

/**
 * Count block elements in HTML content
 * @param {string} htmlContent - HTML content
 * @returns {number} Number of block elements
 */
export const countBlockElements = (htmlContent) => {
  if (!htmlContent) return 0;

  try {
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = htmlContent;
    const blockElements = tempDiv.querySelectorAll(
      "p, div, h1, h2, h3, h4, h5, h6, li, blockquote, pre"
    );
    return blockElements.length;
  } catch (error) {
    return 0;
  }
};

/**
 * Extract all block IDs from processed content
 * @param {string} processedHtml - HTML content with block IDs
 * @returns {Array} Array of block IDs
 */
export const extractBlockIds = (processedHtml) => {
  if (!processedHtml) return [];

  try {
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = processedHtml;

    const elements = tempDiv.querySelectorAll("[id]");
    return Array.from(elements).map((el) => el.id);
  } catch (error) {
    console.error("Failed to extract block IDs:", error);
    return [];
  }
};

/**
 * Find the block ID for a given DOM node
 * @param {Node} node - DOM node
 * @returns {string|null} Block ID or null if not found
 */
export const findBlockId = (node) => {
  let element = node;

  // Traverse up the DOM tree to find an element with an ID
  while (element && element !== document.body) {
    if (element.nodeType === Node.ELEMENT_NODE && element.id) {
      return element.id;
    }
    element = element.parentNode;
  }

  return null;
};

/**
 * Validate that content has proper block structure
 * @param {string} htmlContent - HTML content to validate
 * @returns {boolean} True if content has proper block structure
 */
export const validateBlockStructure = (htmlContent) => {
  if (!htmlContent) return false;

  try {
    const blockIds = extractBlockIds(htmlContent);
    return blockIds.length > 0;
  } catch (error) {
    console.error("Failed to validate block structure:", error);
    return false;
  }
};

/**
 * Create content hash for change detection
 * @param {string} htmlContent - HTML content
 * @returns {string} Content hash
 */
export const createContentHash = (htmlContent) => {
  if (!htmlContent) return "";

  // Simple hash function for content versioning
  let hash = 0;
  for (let i = 0; i < htmlContent.length; i++) {
    const char = htmlContent.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  return Math.abs(hash).toString(36);
};

/**
 * Check if an element is a block-level element
 * @param {Element} element - DOM element to check
 * @returns {boolean} True if block element
 */
export const isBlockElement = (element) => {
  const blockElements = [
    "div",
    "p",
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "ul",
    "ol",
    "li",
    "blockquote",
    "pre",
    "article",
    "section",
    "header",
    "footer",
    "main",
    "aside",
  ];

  return blockElements.includes(element.tagName?.toLowerCase());
};
