import { memo } from "react";
import { HIGHLIGHT_COLORS } from "../utils/constants";
import "./ColorPicker.css";

/**
 * ColorPicker component for selecting highlight colors
 * Displays a predefined palette of colors for text highlighting
 */
const ColorPicker = memo(({ selectedColor, onColorSelect, disabled = false }) => {
  const colors = Object.values(HIGHLIGHT_COLORS);

  const handleColorClick = (color) => {
    if (disabled || color.id === selectedColor?.id) return;
    
    if (onColorSelect) {
      onColorSelect(color);
    }
  };

  const handleKeyDown = (event, color) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleColorClick(color);
    }
  };

  return (
    <div className="color-picker">
      <div className="color-palette">
        {colors.map((color) => (
          <button
            key={color.id}
            className={`color-swatch ${color.id} ${
              selectedColor?.id === color.id ? "active-color" : ""
            } ${disabled ? "disabled" : ""}`}
            style={{
              backgroundColor: color.color,
              borderColor: selectedColor?.id === color.id ? "#007bff" : "transparent"
            }}
            onClick={() => handleColorClick(color)}
            onKeyDown={(e) => handleKeyDown(e, color)}
            disabled={disabled}
            title={`Select ${color.name} highlight color`}
            aria-label={`${color.name} highlight color${
              selectedColor?.id === color.id ? " (selected)" : ""
            }`}
            aria-pressed={selectedColor?.id === color.id}
            tabIndex={disabled ? -1 : 0}
          />
        ))}
      </div>
    </div>
  );
});

// Display name for debugging
ColorPicker.displayName = "ColorPicker";

export default ColorPicker;
