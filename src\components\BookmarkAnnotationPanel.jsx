import { useState, useCallback, useMemo } from "react";
import { useBookmarkStore } from "../stores/bookmarkStore";
import { useHighlightStore } from "../stores/highlightStore";
import "./BookmarkAnnotationPanel.css";

const BookmarkAnnotationPanel = ({ onNavigateToPage, onClose }) => {
  const [activeTab, setActiveTab] = useState("bookmarks");
  const [isLoading, setIsLoading] = useState(false);

  // Store hooks
  const bookmarks = useBookmarkStore((state) => state.bookmarks);
  const removeBookmark = useBookmarkStore((state) => state.removeBookmark);

  // Get highlights for annotations tab
  const highlights = useHighlightStore((state) => state.highlights);

  // Function to extract text from DOM using highlight metadata
  const extractHighlightText = useCallback((highlight) => {
    try {
      // If text is already stored (for backwards compatibility)
      if (highlight.text) {
        return highlight.text;
      }

      // Try to reconstruct text from DOM using web-highlighter metadata
      if (highlight.startMeta && highlight.endMeta) {
        // Get the main content container
        const contentContainer = document.querySelector(
          ".main-content-panel .content-text"
        );

        if (contentContainer) {
          const fullText = contentContainer.textContent || "";
          const startOffset = highlight.startMeta.textOffset || 0;
          const endOffset = highlight.endMeta.textOffset || fullText.length;

          // Extract text using global text offsets
          const extractedText = fullText.substring(startOffset, endOffset);

          if (extractedText.trim()) {
            return extractedText.trim();
          }
        }

        // Alternative approach: try to find by node path if available
        if (highlight.startMeta.$node && highlight.endMeta.$node) {
          try {
            const startNode = highlight.startMeta.$node;
            const endNode = highlight.endMeta.$node;
            const startOffset = highlight.startMeta.offset || 0;
            const endOffset = highlight.endMeta.offset || 0;

            if (startNode === endNode) {
              // Same node
              const text = startNode.textContent || "";
              return text.substring(startOffset, endOffset);
            } else {
              // Different nodes - create a range
              const range = document.createRange();
              range.setStart(startNode, startOffset);
              range.setEnd(endNode, endOffset);
              return range.toString();
            }
          } catch (rangeError) {
            console.warn(
              "Failed to extract text using node references:",
              rangeError
            );
          }
        }
      }

      // Fallback: try to get text from serializedRange if available
      if (highlight.serializedRange?.text) {
        return highlight.serializedRange.text;
      }

      // Try to find existing highlight element in DOM
      if (highlight.id) {
        const highlightElement = document.querySelector(
          `[data-highlight-id="${highlight.id}"]`
        );
        if (highlightElement && highlightElement.textContent) {
          return highlightElement.textContent.trim();
        }

        // Alternative: look for highlight by class
        const highlightByClass = document.querySelector(
          `.highlight-${highlight.id}`
        );
        if (highlightByClass && highlightByClass.textContent) {
          return highlightByClass.textContent.trim();
        }
      }

      // Last resort: return a placeholder with some context
      const colorName = highlight.colorId
        ? highlight.colorId.charAt(0).toUpperCase() + highlight.colorId.slice(1)
        : "Unknown";
      return `${colorName} highlighted text (content not available)`;
    } catch (error) {
      return "Highlighted text (extraction failed)";
    }
  }, []);

  // Process highlights into a flat array for display with extracted text
  const allHighlights = useMemo(() => {
    const highlightList = [];

    Object.keys(highlights).forEach((topicId) => {
      Object.keys(highlights[topicId] || {}).forEach((pageId) => {
        (highlights[topicId][pageId] || []).forEach((highlight) => {
          const extractedText = extractHighlightText(highlight);
          highlightList.push({
            ...highlight,
            topicId,
            pageId,
            type: "highlight",
            displayText: extractedText,
          });
        });
      });
    });

    return highlightList.sort(
      (a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0)
    );
  }, [highlights, extractHighlightText]);

  const handleNavigateToBookmark = useCallback(
    (bookmark) => {
      setIsLoading(true);
      try {
        if (onNavigateToPage) {
          onNavigateToPage(bookmark.topicId, bookmark.pageNumber);
          onClose(); // Close panel after navigation
        }
      } catch (error) {
        console.error("Failed to navigate to bookmark:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [onNavigateToPage, onClose]
  );

  const handleNavigateToHighlight = useCallback(
    (highlight) => {
      setIsLoading(true);
      try {
        if (onNavigateToPage) {
          // Extract page number from pageId or use a default
          const pageNumber = 1; // You might need to extract this from pageId
          onNavigateToPage(highlight.topicId, pageNumber);
          onClose(); // Close panel after navigation
        }
      } catch (error) {
        console.error("Failed to navigate to highlight:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [onNavigateToPage, onClose]
  );

  const handleDeleteBookmark = useCallback(
    (bookmarkId, event) => {
      event.stopPropagation();
      if (window.confirm("Are you sure you want to delete this bookmark?")) {
        removeBookmark(bookmarkId);
      }
    },
    [removeBookmark]
  );

  const formatDate = (dateString) => {
    if (!dateString) return "Unknown date";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatTopicName = (topicId) => {
    // Convert topic ID to readable name
    return topicId
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const getColorName = (colorId) => {
    const colorMap = {
      yellow: "Yellow",
      blue: "Blue",
      green: "Green",
      red: "Red",
      purple: "Purple",
      orange: "Orange",
    };
    return colorMap[colorId] || "Unknown";
  };

  return (
    <div className="bookmark-annotation-panel">
      <div className="panel-overlay" onClick={onClose} />
      <div className="panel-container">
        <div className="panel-header">
          <h2 className="panel-title">
            <span className="panel-icon">📚</span>
            Bookmarks & Annotations
          </h2>
          <button className="close-btn" onClick={onClose} title="Close">
            <span>✕</span>
          </button>
        </div>

        <div className="panel-tabs">
          <button
            className={`tab-btn ${activeTab === "bookmarks" ? "active" : ""}`}
            onClick={() => setActiveTab("bookmarks")}
          >
            <span className="tab-icon">🔖</span>
            <span className="tab-text">Bookmarks</span>
            <span className="tab-count">({bookmarks.length})</span>
          </button>
          <button
            className={`tab-btn ${activeTab === "annotations" ? "active" : ""}`}
            onClick={() => setActiveTab("annotations")}
          >
            <span className="tab-icon">🎨</span>
            <span className="tab-text">Highlights</span>
            <span className="tab-count">({allHighlights.length})</span>
          </button>
        </div>

        <div className="panel-content">
          {isLoading && (
            <div className="loading-overlay">
              <div className="loading-spinner">Loading...</div>
            </div>
          )}

          {activeTab === "bookmarks" && (
            <div className="bookmarks-section">
              {bookmarks.length === 0 ? (
                <div className="empty-state">
                  <div className="empty-icon">📖</div>
                  <h3 className="empty-title">No bookmarks yet</h3>
                  <p className="empty-description">
                    Use the bookmark button in the top-right corner of the Tools
                    Panel to save pages for quick access later.
                  </p>
                </div>
              ) : (
                <div className="bookmarks-list">
                  {bookmarks.map((bookmark) => (
                    <div key={bookmark.id} className="bookmark-item">
                      <div className="bookmark-content">
                        <h4 className="bookmark-title">{bookmark.title}</h4>
                        <div className="bookmark-meta">
                          <span className="bookmark-topic">
                            {formatTopicName(bookmark.topicId)}
                          </span>
                          <span className="bookmark-separator">•</span>
                          <span className="bookmark-page">
                            Page {bookmark.pageNumber}
                          </span>
                          <span className="bookmark-separator">•</span>
                          <span className="bookmark-date">
                            {formatDate(bookmark.createdAt)}
                          </span>
                        </div>
                      </div>
                      <div className="bookmark-actions">
                        <button
                          className="action-btn navigate-btn"
                          onClick={() => handleNavigateToBookmark(bookmark)}
                          title="Go to page"
                          disabled={isLoading}
                        >
                          <span>📍</span>
                        </button>
                        <button
                          className="action-btn delete-btn"
                          onClick={(e) => handleDeleteBookmark(bookmark.id, e)}
                          title="Remove bookmark"
                          disabled={isLoading}
                        >
                          <span>🗑️</span>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === "annotations" && (
            <div className="annotations-section">
              {allHighlights.length === 0 ? (
                <div className="empty-state">
                  <div className="empty-icon">🎨</div>
                  <h3 className="empty-title">No highlights yet</h3>
                  <p className="empty-description">
                    Start highlighting text in your course content to see your
                    saved highlights here. You can highlight text by selecting
                    it and choosing a color.
                  </p>
                </div>
              ) : (
                <div className="highlights-list">
                  {allHighlights.map((highlight) => (
                    <div key={highlight.id} className="highlight-item">
                      <div className="highlight-content">
                        <div className="highlight-header">
                          <div className="highlight-color-indicator">
                            <span
                              className={`color-dot color-${highlight.colorId}`}
                              title={`${getColorName(
                                highlight.colorId
                              )} highlight`}
                            ></span>
                            <span className="color-label">
                              {getColorName(highlight.colorId)}
                            </span>
                          </div>
                          <div className="highlight-meta">
                            <span className="highlight-topic">
                              {formatTopicName(highlight.topicId)}
                            </span>
                            <span className="highlight-separator">•</span>
                            <span className="highlight-date">
                              {formatDate(highlight.createdAt)}
                            </span>
                          </div>
                        </div>

                        {highlight.displayText && (
                          <div className="highlight-text">
                            <span className="highlight-quote">"</span>
                            {highlight.displayText}
                            <span className="highlight-quote">"</span>
                          </div>
                        )}

                        <div className="highlight-context">
                          <span className="context-label">Context:</span>
                          <span className="context-info">
                            Page content • {highlight.pageId}
                          </span>
                        </div>
                      </div>

                      <div className="highlight-actions">
                        <button
                          className="action-btn navigate-btn"
                          onClick={() => handleNavigateToHighlight(highlight)}
                          title="Go to highlighted content"
                          disabled={isLoading}
                        >
                          <span>📍</span>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BookmarkAnnotationPanel;
