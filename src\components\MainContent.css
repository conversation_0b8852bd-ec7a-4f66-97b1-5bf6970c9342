.main-content-panel {
  flex-grow: 1;
  flex-shrink: 1; /* Allow shrinking when DevTools opens */
  height: 100%;
  min-height: 0; /* Allow content to shrink below viewport */
  min-width: 400px; /* Ensure minimum width */
  overflow-y: auto;
  padding: 30px 40px;
  max-width: 800px;
  margin: 0 auto;
  box-sizing: border-box;
  scroll-behavior: smooth;
  /* Hide scrollbars while maintaining scroll functionality */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

/* Hide scrollbar for WebKit browsers */
.main-content-panel::-webkit-scrollbar {
  display: none;
}

.audio-player {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 40px;
  transition: all 0.3s ease;
  background-color: white;
}

.audio-player.disabled {
  opacity: 0.5;
  background-color: rgba(255, 255, 255, 0.3);
}

.audio-player.disabled .play-pause-btn-main {
  cursor: not-allowed;
}

.audio-player.error {
  border-color: #f44336;
  background-color: #fff5f5;
}

.player-top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.audio-title {
  flex: 1;
  margin: 0;
  font-weight: bold;
  color: #333;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.track-counter {
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

.audio-error {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 4px;
  padding: 4px 8px;
  background-color: #ffebee;
  border-radius: 4px;
  font-size: 12px;
}

.error-icon {
  color: #f44336;
  font-weight: bold;
}

.error-text {
  color: #d32f2f;
  flex: 1;
}

.error-retry {
  background: none;
  border: none;
  color: #1976d2;
  cursor: pointer;
  font-size: 14px;
  padding: 2px;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.error-retry:hover {
  background-color: rgba(25, 118, 210, 0.1);
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.play-pause-btn-main {
  background: #333;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-size: 16px;
  line-height: 32px;
  cursor: pointer;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.track-nav-btn {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.track-nav-btn:hover:not(:disabled) {
  background: #545b62;
}

.track-nav-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.5;
}

.player-bottom-row {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #666;
}

.time-display {
  min-width: 40px;
  text-align: center;
  font-family: "Courier New", monospace;
  font-size: 12px;
}

.progress-bar-container {
  flex-grow: 1;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 7px 0;
  position: relative;
}

.progress-bar-container:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

.progress-bar-track {
  width: 100%;
  height: 6px;
  background-color: #eee;
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.progress-bar-buffered {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #ddd;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-bar-current {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #333;
  border-radius: 3px;
  transition: width 0.1s ease;
}

.progress-handle {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background-color: #333;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.progress-bar-container:hover .progress-handle {
  opacity: 1;
}

.progress-bar-container:focus .progress-handle {
  opacity: 1;
}

/* Main Topic Heading - Specific Montserrat Styling */
.lesson-content h1 {
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
  font-size: 18px; /* Default size */
  line-height: 1;
  letter-spacing: normal;
  margin-bottom: 15px;
  color: #333;
  /* Apply leading-trim if supported by browser */
  text-edge: cap alphabetic;
  leading-trim: both;
}

/* Responsive heading sizes based on text size setting */
.text-size-small .lesson-content h1 {
  font-size: 16px;
}

.text-size-normal .lesson-content h1 {
  font-size: 18px;
}

.text-size-large .lesson-content h1 {
  font-size: 20px;
}
.lesson-content p,
.lesson-content ol {
  line-height: 1.7;
  color: #444;
}
.lesson-content ol {
  padding-left: 25px;
}
.lesson-content li {
  margin-bottom: 10px;
}
.lesson-content .description {
  display: inline;
}

.text-size-small {
  font-size: 14px;
}
.text-size-normal {
  font-size: 16px;
}
.text-size-large {
  font-size: 18px;
}

/* HTML Content Styles */
.html-content {
  line-height: 1.7;
  color: #444;
}

.html-content h3 {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
}

.html-content p {
  margin-bottom: 12px;
}

.html-content ul,
.html-content ol {
  padding-left: 25px;
  margin-bottom: 15px;
}

.html-content li {
  margin-bottom: 8px;
}

.html-content strong {
  font-weight: bold;
}

.html-content .diagram-container {
  display: flex;
  gap: 64px;
  margin: 40px 0;
  justify-content: center;
}

.html-content .diagram-image {
  max-width: 300px;
  height: auto;
  border-radius: 8px;
}

/* Simplified content styles */
.lesson-content {
  flex-grow: 1;
  min-height: 400px; /* Ensure minimum height for content area */
  display: flex;
  flex-direction: column;
}

/* Block-based content structure styles */
.highlight-block {
  position: relative;
}

.highlight-block p,
.highlight-block li,
.highlight-block h1,
.highlight-block h2,
.highlight-block h3,
.highlight-block h4,
.highlight-block h5,
.highlight-block h6 {
  line-height: 1.6;
  margin-bottom: 15px;
}

.highlight-block li {
  margin-bottom: 8px;
}

/* Rangy highlight styles */
.rangy-highlight {
  border-radius: 2px;
  transition: all 0.2s ease;
  position: relative;
}

.rangy-highlight:hover {
  opacity: 0.8;
}

.lesson-content-body {
  line-height: 1.7;
  color: #444;
  flex-grow: 1; /* Take up remaining space */
  min-height: 200px; /* Ensure minimum height even when empty */
  /* Enable text selection for highlighting */
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

/* Text Highlighting Styles */
.highlight-wrap {
  cursor: text; /* Changed from pointer to text to indicate text can be selected */
  border-radius: 2px;
  padding: 1px 2px;
  margin: -1px -2px;
  position: relative;
  /* Removed transition and hover effects to allow re-highlighting */
}

/* Allow text selection on highlighted text for re-highlighting with different colors */
.highlight-wrap::selection {
  background: rgba(0, 123, 255, 0.3); /* Show selection with blue background */
}

.highlight-wrap::-moz-selection {
  background: rgba(0, 123, 255, 0.3); /* Show selection with blue background */
}

/* Accessibility improvements for highlights */
.highlight-wrap:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* High contrast mode support for highlights */
@media (prefers-contrast: high) {
  .highlight-wrap {
    border-width: 2px;
    border-style: solid;
  }
}

/* Reduced motion support for highlights */
@media (prefers-reduced-motion: reduce) {
  .highlight-wrap {
    transition: none;
  }
  /* Removed hover transform since hover effects are disabled */
}

/* Responsive image sizing for html-content based on text size */
.text-size-small .html-content .diagram-image {
  max-width: 250px;
}

.text-size-normal .html-content .diagram-image {
  max-width: 300px;
}

.text-size-large .html-content .diagram-image {
  max-width: 350px;
}

/* Mixed content styles (text + images with highlighting) */
.mixed-content {
  line-height: 1.7;
  color: #444;
}

.mixed-content h3 {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
  font-weight: bold;
}

.mixed-content p {
  margin-bottom: 12px;
  font-weight: normal;
}

.mixed-content ul,
.mixed-content ol {
  padding-left: 25px;
  margin-bottom: 15px;
}

.mixed-content li {
  margin-bottom: 8px;
  font-weight: normal;
}

.mixed-content strong {
  font-weight: bold;
}

.mixed-content .diagram-container {
  display: flex;
  gap: 64px;
  margin: 40px 0;
  justify-content: center;
}

.mixed-content .diagram-image {
  max-width: 300px;
  height: auto;
  border-radius: 8px;
}

/* Responsive sizing for mixed-content based on text size */
.text-size-small .mixed-content .diagram-image {
  max-width: 250px;
}

.text-size-normal .mixed-content .diagram-image {
  max-width: 300px;
}

.text-size-large .mixed-content .diagram-image {
  max-width: 350px;
}

.text-size-small .mixed-content h3 {
  font-size: 14px;
}

.text-size-normal .mixed-content h3 {
  font-size: 16px;
}

.text-size-large .mixed-content h3 {
  font-size: 18px;
}

/* No-content state */

.no-content {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-style: italic;
  min-height: 200px; /* Ensure minimum height */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.no-content p {
  margin: 0;
  font-size: 16px;
}

/* Enhanced loading and error states */
.audio-player-skeleton {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 40px;
  background-color: white;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .progress-bar-buffered,
  .progress-bar-current,
  .progress-handle {
    transition: none;
  }

  .audio-player {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .audio-player {
    border-width: 2px;
  }

  .progress-bar-track {
    border: 1px solid #000;
  }

  .progress-handle {
    border-color: #000;
  }
}

/* Focus management for keyboard navigation */
.play-pause-btn-main:focus,
.track-nav-btn:focus,
.error-retry:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .audio-player {
    padding: 12px;
  }

  .player-top-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .audio-controls {
    align-self: flex-end;
  }

  .time-display {
    font-size: 11px;
    min-width: 35px;
  }

  .progress-bar-container {
    height: 24px;
    padding: 9px 0;
  }

  .progress-handle {
    width: 16px;
    height: 16px;
  }
}
