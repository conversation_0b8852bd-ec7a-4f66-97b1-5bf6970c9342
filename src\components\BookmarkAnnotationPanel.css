/* Modal Overlay */
.bookmark-annotation-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.panel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.panel-container {
  position: relative;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 900px;
  height: 85%;
  max-height: 700px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Header */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e8ecef;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.panel-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 12px;
}

.panel-icon {
  font-size: 28px;
}

.close-btn {
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: #6c757d;
}

.close-btn:hover {
  background-color: #f8f9fa;
  color: #495057;
  transform: scale(1.1);
}

.close-btn span {
  font-size: 18px;
  font-weight: 600;
}

/* Tabs */
.panel-tabs {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e8ecef;
}

.tab-btn {
  flex: 1;
  padding: 16px 24px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  color: #6c757d;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.tab-btn:hover {
  background-color: #e9ecef;
  color: #495057;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background-color: white;
}

.tab-icon {
  font-size: 18px;
}

.tab-text {
  font-weight: 600;
}

.tab-count {
  background-color: #e9ecef;
  color: #6c757d;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 4px;
}

.tab-btn.active .tab-count {
  background-color: #007bff;
  color: white;
}

/* Content Area */
.panel-content {
  flex: 1;
  overflow-y: auto;
  position: relative;
  background-color: #fafbfc;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  padding: 20px;
  font-size: 16px;
  color: #007bff;
  font-weight: 600;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 60px 40px;
  color: #6c757d;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 16px;
  line-height: 1.5;
  color: #6c757d;
  margin: 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Bookmarks Section */
.bookmarks-section {
  padding: 24px 32px;
}

.bookmarks-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.bookmark-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  border: 1px solid #e8ecef;
  border-radius: 12px;
  background-color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.bookmark-item:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.bookmark-content {
  flex: 1;
  margin-right: 16px;
}

.bookmark-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

.bookmark-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.bookmark-topic {
  font-weight: 600;
  color: #007bff;
}

.bookmark-page {
  font-weight: 500;
}

.bookmark-date {
  color: #adb5bd;
}

.bookmark-separator {
  color: #dee2e6;
}

.bookmark-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* Highlights Section */
.annotations-section {
  padding: 24px 32px;
}

.highlights-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.highlight-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px;
  border: 1px solid #e8ecef;
  border-radius: 12px;
  background-color: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.highlight-item:hover {
  border-color: #28a745;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
  transform: translateY(-2px);
}

.highlight-content {
  flex: 1;
  margin-right: 16px;
}

.highlight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.highlight-color-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.color-dot.color-yellow {
  background-color: #ffc107;
}

.color-dot.color-blue {
  background-color: #007bff;
}

.color-dot.color-green {
  background-color: #28a745;
}

.color-dot.color-red {
  background-color: #dc3545;
}

.color-dot.color-purple {
  background-color: #6f42c1;
}

.color-dot.color-orange {
  background-color: #fd7e14;
}

.color-label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.highlight-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #6c757d;
}

.highlight-topic {
  font-weight: 600;
  color: #007bff;
}

.highlight-date {
  color: #adb5bd;
}

.highlight-separator {
  color: #dee2e6;
}

.highlight-text {
  background-color: #f8f9fa;
  border-left: 4px solid #28a745;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1.5;
  color: #2c3e50;
  font-style: italic;
}

.highlight-quote {
  color: #28a745;
  font-size: 20px;
  font-weight: bold;
}

.highlight-context {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #6c757d;
}

.context-label {
  font-weight: 600;
}

.context-info {
  color: #adb5bd;
}

.highlight-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* Action Buttons */
.action-btn {
  background: white;
  border: 1px solid #e8ecef;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.navigate-btn:hover:not(:disabled) {
  background-color: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.edit-btn:hover:not(:disabled) {
  background-color: #fff3e0;
  border-color: #ff9800;
  color: #f57c00;
}

.delete-btn:hover:not(:disabled) {
  background-color: #ffebee;
  border-color: #f44336;
  color: #d32f2f;
}

/* Responsive Design */
@media (max-width: 768px) {
  .panel-container {
    width: 95%;
    height: 90%;
    margin: 20px;
  }

  .panel-header {
    padding: 20px 24px;
  }

  .panel-title {
    font-size: 20px;
  }

  .bookmarks-section,
  .annotations-section {
    padding: 20px 24px;
  }

  .bookmark-item,
  .highlight-item {
    flex-direction: column;
    align-items: stretch;
    padding: 16px;
  }

  .bookmark-content,
  .highlight-content {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .bookmark-actions,
  .highlight-actions {
    justify-content: flex-end;
  }

  .tab-btn {
    padding: 12px 16px;
    font-size: 14px;
  }

  .tab-icon {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .panel-container {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }

  .panel-header {
    padding: 16px 20px;
  }

  .panel-title {
    font-size: 18px;
  }

  .bookmarks-section,
  .annotations-section {
    padding: 16px 20px;
  }

  .bookmark-meta,
  .highlight-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .bookmark-separator,
  .highlight-separator {
    display: none;
  }
}
