import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import RichTextEditor from '../RichTextEditor';

// Mock react-simple-wysiwyg
vi.mock('react-simple-wysiwyg', () => ({
  default: ({ value, onChange, children, placeholder }) => (
    <div data-testid="mock-editor">
      <div data-testid="mock-toolbar">{children}</div>
      <div
        data-testid="mock-content"
        contentEditable
        dangerouslySetInnerHTML={{ __html: value }}
        onInput={(e) => onChange({ target: { value: e.target.innerHTML } })}
        placeholder={placeholder}
      />
    </div>
  ),
  BtnBold: ({ title, className }) => (
    <button data-testid="bold-btn" title={title} className={className}>
      B
    </button>
  ),
  BtnItalic: ({ title, className }) => (
    <button data-testid="italic-btn" title={title} className={className}>
      I
    </button>
  ),
  BtnUnderline: ({ title, className }) => (
    <button data-testid="underline-btn" title={title} className={className}>
      U
    </button>
  ),
  BtnBulletList: ({ title, className }) => (
    <button data-testid="bullet-btn" title={title} className={className}>
      •
    </button>
  ),
  Toolbar: ({ children, className }) => (
    <div data-testid="toolbar" className={className}>
      {children}
    </div>
  ),
}));

describe('RichTextEditor', () => {
  it('renders with default placeholder', () => {
    render(<RichTextEditor value="" onChange={() => {}} />);
    
    expect(screen.getByTestId('mock-editor')).toBeInTheDocument();
    expect(screen.getByTestId('mock-toolbar')).toBeInTheDocument();
    expect(screen.getByTestId('mock-content')).toBeInTheDocument();
  });

  it('renders all formatting buttons', () => {
    render(<RichTextEditor value="" onChange={() => {}} />);
    
    expect(screen.getByTestId('bold-btn')).toBeInTheDocument();
    expect(screen.getByTestId('italic-btn')).toBeInTheDocument();
    expect(screen.getByTestId('underline-btn')).toBeInTheDocument();
    expect(screen.getByTestId('bullet-btn')).toBeInTheDocument();
  });

  it('displays the provided value', () => {
    const testValue = '<strong>Bold text</strong>';
    render(<RichTextEditor value={testValue} onChange={() => {}} />);
    
    const content = screen.getByTestId('mock-content');
    expect(content.innerHTML).toBe(testValue);
  });

  it('calls onChange when content changes', () => {
    const mockOnChange = vi.fn();
    render(<RichTextEditor value="" onChange={mockOnChange} />);
    
    const content = screen.getByTestId('mock-content');
    fireEvent.input(content, { target: { innerHTML: '<em>Italic text</em>' } });
    
    expect(mockOnChange).toHaveBeenCalledWith({
      target: { value: '<em>Italic text</em>' }
    });
  });

  it('handles empty value gracefully', () => {
    render(<RichTextEditor value={null} onChange={() => {}} />);
    
    const content = screen.getByTestId('mock-content');
    expect(content.innerHTML).toBe('');
  });

  it('uses custom placeholder', () => {
    const customPlaceholder = 'Enter your custom notes here...';
    render(
      <RichTextEditor 
        value="" 
        onChange={() => {}} 
        placeholder={customPlaceholder}
      />
    );
    
    const content = screen.getByTestId('mock-content');
    expect(content).toHaveAttribute('placeholder', customPlaceholder);
  });
});
