import { create } from "zustand";
import { DEFAULT_HIGHLIGHT_COLOR } from "../utils/constants";

// Manual localStorage helpers
const STORAGE_KEY = "highlight-storage";

const loadFromStorage = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      // Ensure highlights is always an object
      return {
        highlights: parsed.highlights || {},
        selectedColor: parsed.selectedColor || DEFAULT_HIGHLIGHT_COLOR,
      };
    }
    return { highlights: {}, selectedColor: DEFAULT_HIGHLIGHT_COLOR };
  } catch (error) {
    console.warn("Failed to load highlights from storage:", error);
    return { highlights: {}, selectedColor: DEFAULT_HIGHLIGHT_COLOR };
  }
};

const saveToStorage = (state) => {
  try {
    localStorage.setItem(
      STORAGE_KEY,
      JSON.stringify({
        highlights: state.highlights,
        selectedColor: state.selectedColor,
      })
    );
  } catch (error) {
    console.warn("Failed to save highlights to storage:", error);
  }
};

/**
 * Zustand store for managing text highlighting functionality
 * Handles highlight state, color selection, and highlight persistence
 */
export const useHighlightStore = create((set, get) => {
  const initialState = loadFromStorage();

  return {
    // Current selected highlight color
    selectedColor: initialState.selectedColor,

    // Map of highlights by topic/page ID
    // Structure: { topicId: { pageId: [highlights] } }
    highlights: initialState.highlights,

    // Currently active highlighter instance
    highlighter: null,

    // Loading state for highlight operations
    isLoading: false,

    // Error state
    error: null,

    /**
     * Set the selected highlight color
     * @param {Object} color - Color object from HIGHLIGHT_COLORS
     */
    setSelectedColor: (color) => {
      // Update the selected color in the store
      set({ selectedColor: color });

      // Save to localStorage
      saveToStorage(get());

      // Log the color change (existing highlights should persist)
      console.log(
        "✅ Selected color updated to:",
        color.name,
        "- Existing highlights will persist"
      );
    },

    /**
     * Set the highlighter instance
     * @param {Object} highlighter - Web-highlighter instance
     */
    setHighlighter: (highlighter) => {
      set({ highlighter });
    },

    /**
     * Add a new highlight
     * @param {string} topicId - Topic identifier
     * @param {string} pageId - Page identifier
     * @param {Object} highlight - Highlight data
     */
    addHighlight: (topicId, pageId, highlight) => {
      const state = get();
      const highlights = state.highlights || {}; // Defensive check

      console.log(
        `💾 Adding highlight to topicId="${topicId}", pageId="${pageId}"`
      );
      console.log(`💾 Current highlights state:`, highlights);

      const newHighlight = {
        ...highlight,
        id: highlight.id || `highlight-${Date.now()}-${Math.random()}`,
        createdAt: new Date().toISOString(),
      };

      const updatedHighlights = {
        ...highlights,
        [topicId]: {
          ...(highlights[topicId] || {}), // Defensive check
          [pageId]: [...(highlights[topicId]?.[pageId] || []), newHighlight],
        },
      };

      set({ highlights: updatedHighlights });

      // Save to localStorage
      saveToStorage(get());

      console.log(`✅ Highlight added successfully:`, newHighlight.id);
    },

    /**
     * Remove a highlight
     * @param {string} topicId - Topic identifier
     * @param {string} pageId - Page identifier
     * @param {string} highlightId - Highlight identifier
     */
    removeHighlight: (topicId, pageId, highlightId) => {
      const { highlights } = get();

      if (!highlights[topicId]?.[pageId]) return;

      set({
        highlights: {
          ...highlights,
          [topicId]: {
            ...highlights[topicId],
            [pageId]: highlights[topicId][pageId].filter(
              (h) => h.id !== highlightId
            ),
          },
        },
      });
    },

    /**
     * Get highlights for a specific topic and page
     * @param {string} topicId - Topic identifier
     * @param {string} pageId - Page identifier
     * @returns {Array} Array of highlights
     */
    getHighlights: (topicId, pageId) => {
      const { highlights } = get();
      return highlights[topicId]?.[pageId] || [];
    },

    /**
     * Clear all highlights for a topic/page
     * @param {string} topicId - Topic identifier
     * @param {string} pageId - Page identifier (optional)
     */
    clearHighlights: (topicId, pageId = null) => {
      const { highlights } = get();

      if (pageId) {
        // Clear specific page
        set({
          highlights: {
            ...highlights,
            [topicId]: {
              ...highlights[topicId],
              [pageId]: [],
            },
          },
        });
      } else {
        // Clear entire topic
        set({
          highlights: {
            ...highlights,
            [topicId]: {},
          },
        });
      }
    },

    /**
     * Set loading state
     * @param {boolean} loading - Loading state
     */
    setLoading: (loading) => {
      set({ isLoading: loading });
    },

    /**
     * Set error state
     * @param {string|null} error - Error message or null
     */
    setError: (error) => {
      set({ error });
    },

    /**
     * Clear error state
     */
    clearError: () => {
      set({ error: null });
    },

    /**
     * Reset store to initial state
     */
    reset: () => {
      set({
        selectedColor: DEFAULT_HIGHLIGHT_COLOR,
        highlights: {},
        highlighter: null,
        isLoading: false,
        error: null,
      });
    },
  };
});
